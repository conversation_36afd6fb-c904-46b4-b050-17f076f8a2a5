@extends('layouts.app')

@section('content')
    <div class="login-container">
        <div class="login-card">
            <h2 class="login-title">Welcome Back</h2>
            <p class="login-subtitle">Login to your account</p>

            <form method="POST" action="{{ route('login.post') }}">
                @csrf

                <!-- Email -->
                <div class="form-group">
                    <label for="email">Email address</label>
                    <input type="email" id="email" name="email" value="{{ old('email') }}"
                        class="@error('email') input-error @enderror" placeholder="<EMAIL>" required>
                    @error('email')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Password -->
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" class="@error('password') input-error @enderror"
                        placeholder="••••••••" required>
                    @error('password')
                        <span class="error-message">{{ $message }}</span>
                    @enderror

                    <div class="checkbox">
                        <input type="checkbox" id="showPassword">
                        <label for="showPassword">Show Password</label>
                    </div>
                </div>

                <!-- Submit -->
                <button type="submit" class="login-button">Sign In</button>
            </form>
        </div>
    </div>
@endsection

<style>
    .login-container {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 7rem;
    }

    .login-card {
        background-color: #fff;
        border-radius: 12px;
        padding: 2rem;
        width: 100%;
        max-width: 400px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .login-title {
        margin-bottom: 0.25rem;
        text-align: center;
        font-weight: bold;
        font-size: 24px;
        color: #333;
    }

    .login-subtitle {
        margin-bottom: 1.5rem;
        text-align: center;
        color: #666;
        font-size: 14px;
    }

    .form-group {
        margin-bottom: 1.25rem;
    }

    label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        font-size: 14px;
        color: #333;
    }

    input[type="email"],
    input[type="password"] {
        width: 100%;
        padding: 0.75rem;
        border-radius: 6px;
        border: 1px solid #ccc;
        font-size: 14px;
        outline: none;
        transition: border-color 0.2s ease;
    }

    input[type="email"]:focus,
    input[type="password"]:focus {
        border-color: #999;
    }

    .input-error {
        border-color: #e74c3c;
    }

    .error-message {
        font-size: 12px;
        color: #e74c3c;
        margin-top: 0.25rem;
        display: block;
    }

    .checkbox {
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .login-button {
        width: 100%;
        padding: 0.75rem;
        border: none;
        border-radius: 6px;
        background-color: #384357;
        color: white;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .login-button:hover {
        background-color: #2f3748;
    }

    @media (max-width: 500px) {
        .login-card {
            padding: 1.5rem;
        }

        .login-title {
            font-size: 20px;
        }
    }
</style>

<script>
    document.getElementById('showPassword').addEventListener('change', function() {
        const password = document.getElementById('password');
        password.type = this.checked ? 'text' : 'password';
    });
</script>
