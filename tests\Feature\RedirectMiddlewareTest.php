<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class RedirectMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function unauthenticated_user_can_access_login_page()
    {
        $response = $this->get('/login');
        
        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
    }

    /** @test */
    public function authenticated_user_is_redirected_from_login_page()
    {
        $user = User::factory()->create();
        
        $response = $this->actingAs($user)->get('/login');
        
        $response->assertRedirect('/admin/dashboard');
        $response->assertSessionHas('info', 'You are already logged in. Please log out first to access this page.');
    }

    /** @test */
    public function unauthenticated_user_can_access_guest_routes()
    {
        $guestRoutes = ['/', '/about', '/terms', '/privacy', '/pricing', '/contact'];
        
        foreach ($guestRoutes as $route) {
            $response = $this->get($route);
            $response->assertStatus(200);
        }
    }

    /** @test */
    public function authenticated_user_is_redirected_from_guest_routes()
    {
        $user = User::factory()->create();
        
        $guestRoutes = ['/', '/about', '/terms', '/privacy', '/pricing', '/contact'];
        
        foreach ($guestRoutes as $route) {
            $response = $this->actingAs($user)->get($route);
            $response->assertRedirect('/admin/dashboard');
            $response->assertSessionHas('info', 'You are already logged in. Please log out first to access this page.');
        }
    }

    /** @test */
    public function unauthenticated_user_cannot_access_admin_routes()
    {
        $response = $this->get('/admin/dashboard');
        
        $response->assertRedirect('/login');
    }

    /** @test */
    public function authenticated_user_can_access_admin_routes()
    {
        $user = User::factory()->create();
        
        $response = $this->actingAs($user)->get('/admin/dashboard');
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard');
    }
}
