

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('layouts.navbars.auth.topnav', ['title' => 'Dashboard'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="container-fluid py-4">
        <!-- Welcome Message - Classic Design -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-light shadow-sm animate__animated animate__fadeIn bg-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-light rounded-circle p-3 me-3">
                                    <i class="fas fa-user-circle text-muted fa-2x"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h4 class="fw-bold text-dark mb-1 ttl">Welcome, <?php echo e(Auth::user()->name); ?></h4>
                                <p class="text-muted mb-0">You're logged into the admin dashboard. Here's a quick overview.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Metrics - Classic Cards -->
        <div class="row g-4">
            <?php
                $cards = [
                    ['icon' => 'fa-hand-holding-heart', 'title' => 'Contact Us', 'count' => 7, 'bg' => 'bg-light'],
                ];
            ?>

            <?php $__currentLoopData = $cards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $card): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-xl-3 col-md-4 col-sm-6 animate__animated animate__fadeInUp" style="animation-delay: <?php echo e($index * 0.05); ?>s">
                    <div class="card classic-card h-100 border-light shadow-sm overflow-hidden">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="<?php echo e($card['bg']); ?> rounded p-3 me-3">
                                        <i class="fas <?php echo e($card['icon']); ?> fa-lg text-dark"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1"><?php echo e($card['title']); ?></h6>
                                    <h4 class="fw-bold mb-0"><?php echo e($card['count']); ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>