<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Plan;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class PlanController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $plans = Plan::select('plans_id', 'name', 'description', 'price', 'duration', 'status', 'is_popular', 'sort_order', 'created_at')
                    ->orderBy('sort_order', 'asc')
                    ->orderBy('created_at', 'desc')
                    ->get();

                return DataTables::of($plans)
                    ->addIndexColumn()
                    ->editColumn('name', fn($p) => ucwords($p->name ?? ''))
                    ->editColumn('description', fn($p) => Str::limit($p->description ?? '', 50))
                    ->editColumn('price', fn($p) => '$' . number_format($p->price, 2))
                    ->editColumn('duration', fn($p) => ucfirst($p->duration ?? ''))

                    ->editColumn('status', function ($p) {
                        $status = $p->status ? 'Active' : 'Inactive';
                        $btnClass = $p->status ? 'btn-success' : 'btn-secondary';
                        return '<button type="button" class="m-0 btn btn-sm toggle-status ' . $btnClass . '" data-id="' . $p->plans_id . '">' . $status . '</button>';
                    })

                    ->editColumn('is_popular', function ($p) {
                        return $p->is_popular ? '<span class="badge bg-warning">Popular</span>' : '<span class="badge bg-light text-dark">Regular</span>';
                    })

                    ->editColumn('created_at', fn($p) => optional($p->created_at)->format('d M, Y h:i A'))

                    ->addColumn('actions', function ($p) {
                        $id = $p->plans_id;
                        $viewRoute = route('plan.show', $id);
                        $editRoute = route('plan.edit', $id);

                        return '
                        <div class="d-flex gap-2">
                            <a href="' . $viewRoute . '" class="text-info ttt" title="View">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="' . $editRoute . '" class="text-primary ttt" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button" class="text-danger border-0 bg-transparent p-0 m-0 delete-button ttt" data-id="' . $id . '" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>';
                    })

                    ->rawColumns(['status', 'is_popular', 'actions'])
                    ->make(true);
            }

            return view('admin.plan.index');
        } catch (Exception $e) {
            Log::error('Plan index error: ' . $e->getMessage());
            return response()->json(['error' => 'Something went wrong'], 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.plan.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|min:3',
                'description' => 'nullable|string|max:1000',
                'price' => 'required|numeric|min:0',
                'duration' => 'required|in:weekly,monthly,yearly',
                'features' => 'nullable|array',
                'features.*' => 'string|max:255',
                'status' => 'required|in:0,1',
                'is_popular' => 'required|in:0,1',
                'sort_order' => 'nullable|integer|min:0',
            ]);

            $plan = new Plan([
                'plans_id' => (string) Str::uuid(),
                'name' => $validated['name'],
                'description' => $validated['description'],
                'price' => $validated['price'],
                'duration' => $validated['duration'],
                'features' => $validated['features'] ?? [],
                'status' => $validated['status'] ?? 1,
                'is_popular' => $validated['is_popular'] ?? 0,
                'sort_order' => $validated['sort_order'] ?? 0,
            ]);

            $plan->save();

            return redirect()->route('plan.index')->with('success', 'Plan created successfully!');
        } catch (Exception $e) {
            Log::error('Plan store error: ' . $e->getMessage());
            return redirect()->back()->withErrors(['error' => 'Failed to create plan. Please try again.'])->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $plan = Plan::where('plans_id', $id)->firstOrFail();
        return view('admin.plan.show', compact('plan'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $plan = Plan::where('plans_id', $id)->firstOrFail();
        return view('admin.plan.edit', compact('plan'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $plan = Plan::where('plans_id', $id)->firstOrFail();

            $validated = $request->validate([
                'name' => 'required|string|max:255|min:3',
                'description' => 'nullable|string|max:1000',
                'price' => 'required|numeric|min:0',
                'duration' => 'required|in:weekly,monthly,yearly',
                'features' => 'nullable|array',
                'features.*' => 'string|max:255',
                'status' => 'required|in:0,1',
                'is_popular' => 'required|in:0,1',
                'sort_order' => 'nullable|integer|min:0',
            ]);

            $plan->update([
                'name' => $validated['name'],
                'description' => $validated['description'],
                'price' => $validated['price'],
                'duration' => $validated['duration'],
                'features' => $validated['features'] ?? [],
                'status' => $validated['status'],
                'is_popular' => $validated['is_popular'],
                'sort_order' => $validated['sort_order'] ?? 0,
            ]);

            return redirect()->route('plan.index')->with('success', 'Plan updated successfully!');
        } catch (Exception $e) {
            Log::error('Plan update error: ' . $e->getMessage());
            return redirect()->back()->withErrors(['error' => 'Failed to update plan. Please try again.'])->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $plan = Plan::where('plans_id', $id)->firstOrFail();
            $plan->delete();

            return response()->json(['success' => 'Plan deleted successfully!']);
        } catch (Exception $e) {
            Log::error('Plan delete error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete plan. Please try again.'], 500);
        }
    }

    /**
     * Toggle plan status
     */
    public function status(string $id)
    {
        try {
            $plan = Plan::where('plans_id', $id)->firstOrFail();
            $plan->status = !$plan->status;
            $plan->save();

            return response()->json(['success' => 'Plan status updated successfully!']);
        } catch (Exception $e) {
            Log::error('Plan status error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update plan status. Please try again.'], 500);
        }
    }
}
