<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use App\Http\Controllers\Web\AuthController;
use App\Http\Controllers\Admin\FaqController;
use App\Http\Controllers\Admin\HomeController;
use App\Http\Controllers\Guest\MainController;
use App\Http\Controllers\Admin\NewsController;
use App\Http\Controllers\Admin\BlogController;
use App\Http\Controllers\Admin\EventController;
use App\Http\Controllers\Admin\BannerController;
use App\Http\Controllers\Admin\CharityController;
use App\Http\Controllers\Admin\FaqTypeController;
use App\Http\Controllers\Admin\FoundationController;
use App\Http\Controllers\Admin\TestimonialController;

Route::get('/cache', function () {
    Artisan::call('optimize:clear');
    return redirect()->route('login')->with('success', 'All caches cleared successfully!');
});
Route::middleware(['redirect'])->group(function () {
    // Guest Routes
    Route::get('/', [MainController::class, 'index'])->name('home');
    Route::get('/about', [MainController::class, 'about'])->name('about');
    Route::get('/terms', [MainController::class, 'terms'])->name('terms');
    Route::get('/privacy', [MainController::class, 'privacy'])->name('privacy');
    Route::get('/pricing', [MainController::class, 'pricing'])->name('pricing');
    Route::get('/contact', [MainController::class, 'contact'])->name('contact');

    // Auth Routes
    Route::get('login', [AuthController::class, 'index'])->name('login');
    Route::post('login-post', [AuthController::class, 'login'])->name('login.post');
});

// Logout
Route::middleware('auth')->prefix('admin')->group(function () {
    Route::post('logout', [AuthController::class, 'logout'])->name('logout');

    Route::get('dashboard', [HomeController::class, 'index'])->name('dashboard');
    Route::get('profile', [HomeController::class, 'showProfile'])->name('profile.show');
    Route::put('profile-update', [HomeController::class, 'updateProfile'])->name('profile.update');

    Route::resource('foundation', FoundationController::class)->except(['create', 'store', 'edit', 'update']);

    Route::resource('testimonial', TestimonialController::class);
    Route::post('testimonial/{id}/status', [TestimonialController::class, 'status'])->name('testimonial.status');

    Route::resource('faq-type', FaqTypeController::class);

    Route::resource('faq', FaqController::class);
    Route::post('faq/{id}/status', [FaqController::class, 'status'])->name('faq.status');

    Route::resource('banner', BannerController::class)->except(['show']);
    Route::post('banner/{id}/status', [BannerController::class, 'status'])->name('banner.status');

    Route::resource('blog', BlogController::class);
    Route::post('blog/{id}/status', [BlogController::class, 'status'])->name('blog.status');
    Route::post('ckeditor-upload', [BlogController::class, 'upload'])->name('ckeditor.upload');

    Route::resource('news', NewsController::class);
    Route::post('news/{id}/status', [NewsController::class, 'status'])->name('news.status');
    Route::post('news-ckeditor', [NewsController::class, 'upload'])->name('news.ckeditor.upload');

    Route::get('charity', [CharityController::class, 'index'])->name('charity.index');
    Route::get('charity/show/{id?}', [CharityController::class, 'show'])->name('charity.show');

    Route::resource('events', EventController::class);
    Route::post('event/{id}/status', [EventController::class, 'status'])->name('events.status');
});
