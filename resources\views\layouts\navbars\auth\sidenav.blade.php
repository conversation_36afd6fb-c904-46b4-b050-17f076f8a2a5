<style>
    .sidenav-scrollable {
        overflow: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .sidenav-scrollable::-webkit-scrollbar {
        width: 0px;
        height: 0px;
    }

    .sidenav-scrollable:hover {
        scrollbar-width: thin;
        scrollbar-color: rgba(103, 116, 142, 0.4) transparent;
    }

    .sidenav-scrollable:hover::-webkit-scrollbar {
        width: 6px;
    }

    .sidenav-scrollable:hover::-webkit-scrollbar-thumb {
        background-color: rgba(103, 116, 142, 0.4);
        border-radius: 3px;
    }

    .sidenav-scrollable:hover::-webkit-scrollbar-track {
        background-color: transparent;
    }
</style>

<aside class="sidenav bg-white navbar navbar-vertical navbar-expand-xs border-0 border-radius-xl my-3 fixed-start ms-4"
    id="sidenav-main" style="height: 100vh; overflow: hidden;">

    <!-- Logo -->
    <div class="sidenav-header">
        <a class="navbar-brand d-flex justify-content-center me-0 pb-0" href="{{ route('dashboard') }}">
            {{-- <img src="{{ asset('assets/images/Logo.png') }}" class="navbar-brand-img img-fluid" alt="main_logo"
                style="max-height: 80px;"> --}}
                <h4>Trash</h4>
        </a>
        <i class="fas fa-times p-3 cursor-pointer text-secondary opacity-5 position-absolute end-0 top-0 d-xl-none"
            aria-hidden="true" id="iconSidenav"></i>
    </div>

    <hr class="horizontal dark mt-3">

    <!-- ✅ Scrollable Nav Content -->
    <div class="sidenav-scrollable overflow-auto px-2" style="height: calc(100vh - 150px); scroll-behavior: smooth;">
        <ul class="navbar-nav">

            <!-- Dashboard -->
            <li class="nav-item">
                <a class="nav-link {{ request()->is('dashboard') ? 'active text-primary fw-bold' : '' }}"
                    href="{{ route('dashboard') }}">
                    <div
                        class="icon icon-shape icon-sm border-radius-md text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="fa-solid fa-house" style="color: #67748e;"></i>
                    </div>
                    <span class="nav-link-text ms-1">Dashboard</span>
                </a>
            </li>

            <!-- Settings -->
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('profile.show') ? 'active text-primary fw-bold' : '' }}"
                    href="{{ route('profile.show') }}">
                    <div
                        class="icon icon-shape icon-sm border-radius-md text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="fa-solid fa-gear" style="color: #67748e;"></i>
                    </div>
                    <span class="nav-link-text ms-1">Settings</span>
                </a>
            </li> 
        </ul>
    </div>
</aside>
