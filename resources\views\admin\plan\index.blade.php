@extends('layouts.app')

@section('content')
    @include('layouts.navbars.auth.topnav', ['title' => 'Plans Management'])

    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header pb-0 d-flex justify-content-between align-items-center">
                        <h6>Plans List</h6>
                        <a href="{{ route('plan.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i> Add New Plan
                        </a>
                    </div>
                    <div class="card-body px-0 pt-0 pb-2">
                        <div class="table-responsive p-0">
                            <table class="table align-items-center mb-0" id="plansTable">
                                <thead>
                                    <tr>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">#</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Name</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Description</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Price</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Duration</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Popular</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Created At</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#plansTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "{{ route('plan.index') }}",
            type: 'GET'
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'description', name: 'description' },
            { data: 'price', name: 'price' },
            { data: 'duration', name: 'duration' },
            { data: 'status', name: 'status', orderable: false },
            { data: 'is_popular', name: 'is_popular', orderable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[7, 'desc']],
        responsive: true,
        language: {
            processing: '<div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div>',
            emptyTable: "No plans found",
            zeroRecords: "No matching plans found"
        }
    });

    // Toggle Status
    $(document).on('click', '.toggle-status', function() {
        var planId = $(this).data('id');
        var button = $(this);
        
        $.ajax({
            url: "{{ route('plan.status', ':id') }}".replace(':id', planId),
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    table.ajax.reload(null, false);
                    toastr.success(response.success);
                } else {
                    toastr.error(response.error || 'Something went wrong');
                }
            },
            error: function(xhr) {
                var errorMsg = xhr.responseJSON?.error || 'Something went wrong';
                toastr.error(errorMsg);
            }
        });
    });

    // Delete Plan
    $(document).on('click', '.delete-button', function() {
        var planId = $(this).data('id');
        
        if (confirm('Are you sure you want to delete this plan?')) {
            $.ajax({
                url: "{{ route('plan.destroy', ':id') }}".replace(':id', planId),
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        table.ajax.reload(null, false);
                        toastr.success(response.success);
                    } else {
                        toastr.error(response.error || 'Something went wrong');
                    }
                },
                error: function(xhr) {
                    var errorMsg = xhr.responseJSON?.error || 'Something went wrong';
                    toastr.error(errorMsg);
                }
            });
        }
    });
});
</script>
@endpush
