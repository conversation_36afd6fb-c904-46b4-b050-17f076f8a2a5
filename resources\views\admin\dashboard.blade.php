@extends('layouts.app')

@section('content')
    @include('layouts.navbars.auth.topnav', ['title' => 'Dashboard'])

    <div class="container-fluid py-4">
        <!-- Welcome Message - Classic Design -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-light shadow-sm animate__animated animate__fadeIn bg-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-light rounded-circle p-3 me-3">
                                    <i class="fas fa-user-circle text-muted fa-2x"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h4 class="fw-bold text-dark mb-1 ttl">Welcome, {{ Auth::user()->name }}</h4>
                                <p class="text-muted mb-0">You're logged into the admin dashboard. Here's a quick overview.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Metrics - Classic Cards -->
        <div class="row g-4">
            @php
                $cards = [
                    ['icon' => 'fa-hand-holding-heart', 'title' => 'Contact Us', 'count' => 7, 'bg' => 'bg-light'],
                ];
            @endphp

            @foreach ($cards as $index => $card)
                <div class="col-xl-3 col-md-4 col-sm-6 animate__animated animate__fadeInUp" style="animation-delay: {{ $index * 0.05 }}s">
                    <div class="card classic-card h-100 border-light shadow-sm overflow-hidden">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="{{ $card['bg'] }} rounded p-3 me-3">
                                        <i class="fas {{ $card['icon'] }} fa-lg text-dark"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">{{ $card['title'] }}</h6>
                                    <h4 class="fw-bold mb-0">{{ $card['count'] }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
@endsection